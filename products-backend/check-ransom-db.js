const mongoose = require('mongoose');
require('dotenv').config();

async function checkRansomDatabase() {
  try {
    console.log('🔍 检查勒索软件数据库...');
    
    // 连接到MongoDB服务器
    const connectionString = process.env.MONGODB_URI.replace('/products', '');
    await mongoose.connect(connectionString);
    console.log('✅ MongoDB连接成功');
    
    // 检查勒索软件数据库
    const ransomDb = mongoose.connection.client.db('READ__ME_TO_RECOVER_YOUR_DATA');
    const readmeCollection = ransomDb.collection('README');
    
    console.log('\n🚨 勒索软件信息:');
    const ransomNote = await readmeCollection.findOne();
    if (ransomNote) {
      console.log('勒索信息内容:');
      console.log(JSON.stringify(ransomNote, null, 2));
    }
    
    // 检查是否有备份数据库
    console.log('\n🔍 检查可能的备份数据库...');
    const adminDb = mongoose.connection.db.admin();
    const databases = await adminDb.listDatabases();
    
    const backupDatabases = databases.databases.filter(db => 
      db.name.includes('backup') || 
      db.name.includes('bak') || 
      db.name.includes('old') ||
      db.name.includes('products') && db.name !== 'products'
    );
    
    if (backupDatabases.length > 0) {
      console.log('找到可能的备份数据库:');
      for (const db of backupDatabases) {
        console.log(`  - ${db.name} (大小: ${(db.sizeOnDisk / 1024 / 1024).toFixed(2)} MB)`);
        
        // 检查备份数据库的集合
        try {
          const backupDb = mongoose.connection.client.db(db.name);
          const collections = await backupDb.listCollections().toArray();
          
          for (const collection of collections) {
            if (collection.name.toLowerCase().includes('product')) {
              const collectionObj = backupDb.collection(collection.name);
              const count = await collectionObj.countDocuments();
              console.log(`    ${collection.name}: ${count} 个文档`);
            }
          }
        } catch (error) {
          console.log(`    ❌ 无法访问: ${error.message}`);
        }
      }
    } else {
      console.log('❌ 没有找到备份数据库');
    }
    
  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 MongoDB连接已关闭');
  }
}

checkRansomDatabase();
