const mongoose = require('mongoose');
require('dotenv').config();

async function checkAllDatabases() {
  try {
    console.log('🔍 连接到MongoDB服务器...');
    
    // 连接到MongoDB服务器（不指定具体数据库）
    const connectionString = process.env.MONGODB_URI.replace('/products', '');
    await mongoose.connect(connectionString);
    console.log('✅ MongoDB连接成功');
    
    const adminDb = mongoose.connection.db.admin();
    
    // 列出所有数据库
    console.log('\n📊 所有数据库:');
    const databases = await adminDb.listDatabases();
    databases.databases.forEach(db => {
      console.log(`  - ${db.name} (大小: ${(db.sizeOnDisk / 1024 / 1024).toFixed(2)} MB)`);
    });
    
    // 检查每个数据库的集合
    for (const dbInfo of databases.databases) {
      if (dbInfo.name === 'admin' || dbInfo.name === 'config' || dbInfo.name === 'local') {
        continue; // 跳过系统数据库
      }
      
      console.log(`\n🗂️  数据库 "${dbInfo.name}" 的集合:`);
      
      try {
        const db = mongoose.connection.client.db(dbInfo.name);
        const collections = await db.listCollections().toArray();
        
        if (collections.length === 0) {
          console.log('  (无集合)');
        } else {
          for (const collection of collections) {
            const collectionObj = db.collection(collection.name);
            const count = await collectionObj.countDocuments();
            console.log(`  - ${collection.name}: ${count} 个文档`);
            
            // 如果是产品相关的集合，显示一些示例数据
            if (collection.name.toLowerCase().includes('product') && count > 0) {
              console.log('    示例文档:');
              const sample = await collectionObj.findOne();
              if (sample) {
                console.log(`    ID: ${sample._id}`);
                if (sample.name) console.log(`    名称: ${sample.name}`);
                if (sample.productId) console.log(`    产品ID: ${sample.productId}`);
                if (sample.category) console.log(`    分类: ${sample.category}`);
              }
            }
          }
        }
      } catch (error) {
        console.log(`  ❌ 无法访问数据库 ${dbInfo.name}: ${error.message}`);
      }
    }
    
  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 MongoDB连接已关闭');
  }
}

checkAllDatabases();
